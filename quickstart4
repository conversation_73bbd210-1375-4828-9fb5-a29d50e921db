Quickstart #4: Compound Actions: Beginner
Welcome to the beginner's quickstart guide for Moveworks Compound Actions! This guide is designed to help you understand the fundamentals of creating multi-action integrations with your business systems using the Compound Action YAML language.
1. What are Compound Actions?
At their core, Compound Actions are workflows that allow developers to orchestrate multiple individual actions into a unified automation sequence.1 These individual actions can include
•	HTTP calls to external systems (like ServiceNow, Salesforce, Workday).2
•	Execution of custom API scripts for bespoke logic.
•	Leveraging a rich library of Built-in Actions provided by the Moveworks platform.1
The primary purpose of Compound Actions is to enable the execution of complex, multi-step tasks by logically combining these diverse actions into a single, manageable unit.1
2. Fundamentals of Compound Action YAML
Compound Actions are defined using YAML (YAML Ain't Markup Language), a human-readable data serialization language.
2.1. Basic Structure and the steps Key
The cornerstone of a Compound Action's YAML definition, particularly when multiple operations are involved, is the steps key. The steps key introduces a list, where each item in the list represents an individual action or expression to be executed.3 These steps are performed sequentially in the order they appear in the list.
•	Single Expression Compound Action: For Compound Actions that contain only a single action or expression, using the steps key is optional. The action can be specified directly at the top level of the compound action definition.3
•	Multiple Expressions Compound Action: When a Compound Action includes multiple actions or expressions, encapsulating them within a steps list under the steps key is required.3 This clearly defines the execution order.
A basic example illustrating the steps key with multiple actions:
YAML
steps:
  - action: # First action to be executed
      action_name: example_action_1_name
      output_key: result_from_action1 # Output will be stored here
      input_args:
        example_input_1: "Value for Action 1"
  - action: # Second action, executed after the first one completes
      action_name: example_action_2_name
      output_key: result_from_action2
      input_args:
        example_input_2: "Value for Action 2"
        input_from_previous_step: data.result_from_action1.some_field # Using output from previous step
In this structure, example_action_1_name would execute first. Upon its completion, example_action_2_name would execute. The output_key is used to name the variable that will hold the result of each action, and these results are typically accessible via the data object (e.g., data.result_from_action1). If the output of a step isn't needed, use an underscore (_) as the output_key.5
2.2. Defining Input Variables
Input Variables are crucial for making Compound Actions dynamic and reusable. These variables define the data that the Compound Action needs to begin its execution.1
When a Compound Action is defined (often through the Creator Studio interface), its input fields are specified. These input variables, once provided during the execution of the Compound Action, are inserted at the top level of the data key within the Compound Action's execution context.6 This means they become readily accessible throughout the various steps of the Compound Action.
For example, if a Compound Action is defined with an input variable target_email, it can be accessed within the YAML steps as data.target_email.
3. Your First Compound Action: A Simple Sequence
Let's create a basic Compound Action with a couple of sequential steps. This will introduce the core YAML structure and the concept of ordered execution.
Focus:
•	Basic YAML structure using the steps key.
•	Defining a simple action step.
•	Understanding how output_key stores the result of an action.
•	Accessing results via the data object.
Example Scenario:
Fetch details for a user given their email and then use a simple script to format a personalized greeting message. This involves:
1.	An input variable for the user's email.
2.	A built-in action to retrieve user information.
3.	A script action to construct the greeting.
Step-by-step Instructions:
1.	Define Input Variable: In your Compound Action definition (e.g., via Creator Studio), create an input variable, say email_address of type string.
2.	YAML Structure:
YAML
steps:
  - action:
      action_name: mw.get_user_by_email # Built-in action to fetch user details
      output_key: fetched_user_info
      input_args:
        user_email: data.email_address # Using the input variable

  - script:
      output_key: greeting_message
      input_args:
        user_full_name: data.fetched_user_info.user.full_name # Accessing a field from the previous action's output
      code: |
        # APIthon code to create a greeting
        greeting = "Hello, " + user_full_name + "! Welcome."
        greeting # This is the return value of the script
In this example, the first step calls mw.get_user_by_email 7, passing the data.email_address input. The result is stored in data.fetched_user_info. The second step, a script action, takes the full_name from data.fetched_user_info.user.full_name as an input argument (user_full_name) and constructs a greeting string, which is then stored in data.greeting_message.
4. Integrating Multiple Actions
Compound Actions excel at orchestrating sequences of different types of actions.
4.1. Calling HTTP Actions
HTTP Actions are fundamental for integrating Moveworks with external business systems and APIs (e.g., ServiceNow, Salesforce, Workday).2 Within a Compound Action, an HTTP Action is invoked by referencing its pre-configured action_name. The actual configuration of the HTTP Action (including endpoint URL, authentication method, headers, etc.) is typically done in the Creator Studio, separate from the Compound Action YAML.8
The YAML syntax for an HTTP action step generally includes 3:
•	action_name: The unique identifier of the configured HTTP Action.
•	output_key: The variable name to store the response from the HTTP call.
•	input_args: A dictionary of arguments to pass to the HTTP Action (e.g., query parameters, request body components).
•	progress_updates (optional): Messages displayed to the user while the action is pending and upon completion.
YAML Snippet (Conceptual):
YAML
steps:
  - action:
      action_name: retrieve_customer_order_details # Name of a pre-configured HTTP Action
      output_key: customer_order_response
      input_args:
        order_id: data.input_order_id # Assuming 'input_order_id' is an input variable
        customer_segment: "premium"
      progress_updates:
        on_pending: "Retrieving order details for order ID: {{data.input_order_id}}..."
        on_complete: "Order details retrieved successfully."

  # A subsequent step could use data.customer_order_response
  - script:
      output_key: order_summary
      input_args:
        order_data: data.customer_order_response.body # Assuming response body contains order info
      code: |
        # APIthon to process order_data and create a summary
        summary = "Order Total: $" + str(order_data.total_amount) # Example
        summary
4.2. Using Basic Built-in Actions
Moveworks provides a range of built-in actions, also known as Native Actions, which can be directly invoked within Compound Actions using the mw.{{action_name}} syntax.7 These actions simplify common tasks such as sending chat notifications or retrieving user information.
Example 7:
Assume a previous step in the Compound Action has retrieved a user's record ID and stored it in data.target_user_object.user.id.
YAML
steps:
  #... previous steps...

  - action:
      action_name: mw.send_plaintext_chat_notification
      output_key: chat_notification_status # Stores the result of the send operation
      input_args:
        user_record_id: data.target_user_object.user.id # Target user's record ID
        message: "Your request has been processed. Reference ID: {{data.request_reference_id}}" # Personalized message
      progress_updates:
        on_pending: "Sending notification..."
        on_complete: "Notification sent."
Here, mw.send_plaintext_chat_notification is called to send a message. The user_record_id and message are provided as input arguments. The message itself can be dynamic, incorporating data from other parts of the Compound Action (like data.request_reference_id in this example) using {{}} template syntax.
5. Basic YAML for Sequential Logic
As outlined previously, the steps list is the fundamental construct for defining sequential logic.3 Actions listed under steps are executed one after another, in the order they appear in the YAML. The completion of one step triggers the execution of the next.
6. Handling Inputs and Outputs (output_key, basic data access)
Effective data flow is critical for multi-step automations. In Compound Actions, this is primarily managed through output_key and the data object.
•	output_key: Every action or script step that produces a result requires an output_key to be defined.3 This key serves as the variable name under which the step's output (e.g., API response, script return value) will be stored. If the output is not needed, use _.5
•	data Object: The data object acts as a central repository or shared memory for the Compound Action's execution.6 It holds:
o	The initial input variables provided to the Compound Action.
o	The outputs of all completed steps, stored under their respective output_keys.
Subsequent steps in the Compound Action can then access these stored values using the pattern data.your_output_key.some_field_in_the_output or data.your_input_variable.
Example of Data Flow:
YAML
steps:
  - action:
      action_name: get_initial_record # Assume this action returns { "id": "123", "status": "pending" }
      output_key: step1_result
      input_args:
        record_name: data.input_record_name

  - script:
      action_name: process_record_status # Note: script actions don't have an 'action_name' field, this is illustrative
      output_key: step2_processed_data
      input_args:
        current_status: data.step1_result.status # Accessing 'status' from step1_result
        record_identifier: data.step1_result.id # Accessing 'id' from step1_result
      code: |
        # APIthon code
        new_status_message = "Record " + record_identifier + " is currently " + current_status + "."
        new_status_message # This is returned

  - action:
      action_name: mw.send_plaintext_chat_notification
      output_key: notification_status
      input_args:
        user_record_id: data.requestor.user.id # Assuming requestor info is available via data.requestor
        message: data.step2_processed_data # Using the full output from step 2
In this flow, step1_result holds the output of the first action. The script in the second step uses fields from data.step1_result. Finally, the notification action uses the entire output from the script, data.step2_processed_data, as its message.
7. Beginner Practical Example: Fetching User Data and Sending a Notification
This complete example combines concepts from this beginner quickstart to perform a common IT support task: looking up a user and sending them a basic notification.
Scenario: An employee reports an issue, and an automated process needs to acknowledge this by sending them a personalized chat message.
Steps:
1.	Input Variable: The Compound Action will take the user's email address as input. Let's call it user_email_input.
2.	Fetch User Details: Use the mw.get_user_by_email built-in action to retrieve the user's full details, including their record ID (needed for sending a notification) and full name (for personalization).
3.	Send Notification: Use the mw.send_plaintext_chat_notification built-in action to send a message to the user.
YAML Implementation:
YAML
# Input variable defined in Creator Studio:
# - user_email_input (string), e.g., "[[email protected]](https://help.moveworks.com/cdn-cgi/l/email-protection)"

steps:
  - action:
      action_name: mw.get_user_by_email
      output_key: user_lookup_result
      input_args:
        user_email: data.user_email_input # Using the defined input variable
      progress_updates:
        on_pending: "Looking up user details for {{data.user_email_input}}..."
        on_complete: "User details retrieved."

  - action:
      action_name: mw.send_plaintext_chat_notification
      output_key: notification_dispatch_status
      input_args:
        # User record ID is required by send_plaintext_chat_notification
        # It's found within the output of mw.get_user_by_email
        user_record_id: data.user_lookup_result.user.id
        # Constructing a personalized message using the user's full name
        message: $CONCAT(["Hello ", data.user_lookup_result.user.full_name, ", we have received your request and will look into it shortly."], "", TRUE)
        # The $CONCAT is a Moveworks Data Mapping Syntax function.
        # Alternatively, a script action could be used for more complex message formatting.
      progress_updates:
        on_pending: "Sending acknowledgment to {{data.user_lookup_result.user.full_name}}..."
        on_complete: "Acknowledgment sent."

  - return: # Optionally, return a status or summary
      output_mapper:
        final_status: "'Notification sent to ' + data.user_lookup_result.user.full_name"
        user_record_id_processed: data.user_lookup_result.user.id
Explanation:
•	The Compound Action starts by using mw.get_user_by_email with the provided data.user_email_input. The output, containing the user object, is stored in data.user_lookup_result.7
•	The second action, mw.send_plaintext_chat_notification, then uses data.user_lookup_result.user.id to target the correct user and data.user_lookup_result.user.full_name for a personalized message. The message concatenation is shown using $CONCAT, a function from Moveworks Data Mapping Syntax (DSL).9
•	Finally, a return step is included to output a summary of the operation. This step is optional and demonstrates how you can control the final output of your Compound Action.3
This example demonstrates a simple yet complete multi-action integration using basic YAML, input variables, built-in actions, and data flow between steps. As you become more familiar with these concepts, you'll be able to build increasingly sophisticated automations.
Sure, I'd be happy to help with that! Here's a more organized and easier-to-read version of your document:
________________________________________
Quickstart #4: Compound Actions: Beginner
Welcome to the beginner's quickstart guide for Moveworks Compound Actions! This guide is designed to help you understand the fundamentals of creating multi-action integrations with your business systems using the Compound Action YAML language.
1. What are Compound Actions?
At their core, Compound Actions are workflows that allow developers to orchestrate multiple individual actions into a unified automation sequence. These individual actions can include:
•	HTTP calls to external systems (like ServiceNow, Salesforce, Workday).
•	Execution of custom APIthon scripts for bespoke logic.
•	Leveraging a rich library of Built-in Actions provided by the Moveworks platform.
The primary purpose of Compound Actions is to enable the execution of complex, multi-step tasks by logically combining these diverse actions into a single, manageable unit.
2. Fundamentals of Compound Action YAML
Compound Actions are defined using YAML (YAML Ain't Markup Language), a human-readable data serialization language.
2.1. Basic Structure and the steps Key
The cornerstone of a Compound Action's YAML definition, particularly when multiple operations are involved, is the steps key. The steps key introduces a list, where each item in the list represents an individual action or expression to be executed. These steps are performed sequentially in the order they appear in the list.
Single Expression Compound Action
For Compound Actions that contain only a single action or expression, using the steps key is optional. The action can be specified directly at the top level of the compound action definition.
Multiple Expressions Compound Action
When a Compound Action includes multiple actions or expressions, encapsulating them within a steps list under the steps key is required. This clearly defines the execution order.
Example:
steps:
  - action: # First action to be executed
      action_name: example_action_1_name
      output_key: result_from_action1 # Output will be stored here
      input_args:
        example_input_1: "Value for Action 1"
  - action: # Second action, executed after the first one completes
      action_name: example_action_2_name
      output_key: result_from_action2
      input_args:
        example_input_2: "Value for Action 2"
        input_from_previous_step: data.result_from_action1.some_field # Using output from previous step
In this structure, example_action_1_name would execute first. Upon its completion, example_action_2_name would execute. The output_key is used to name the variable that will hold the result of each action, and these results are typically accessible via the data object (e.g., data.result_from_action1). If the output of a step isn't needed, use an underscore (_) as the output_key.
2.2. Defining Input Variables
Input Variables are crucial for making Compound Actions dynamic and reusable. These variables define the data that the Compound Action needs to begin its execution.
When a Compound Action is defined (often through the Creator Studio interface), its input fields are specified. These input variables, once provided during the execution of the Compound Action, are inserted at the top level of the data key within the Compound Action's execution context. This means they become readily accessible throughout the various steps of the Compound Action.
For example, if a Compound Action is defined with an input variable target_email, it can be accessed within the YAML steps as data.target_email.
3. Your First Compound Action: A Simple Sequence
Let's create a basic Compound Action with a couple of sequential steps. This will introduce the core YAML structure and the concept of ordered execution.
Focus:
•	Basic YAML structure using the steps key.
•	Defining a simple action step.
•	Understanding how output_key stores the result of an action.
•	Accessing results via the data object.
Example Scenario: Fetch details for a user given their email and then use a simple script to format a personalized greeting message. This involves:
•	An input variable for the user's email.
•	A built-in action to retrieve user information.
•	A script action to construct the greeting.
Step-by-step Instructions:
1.	Define Input Variable: In your Compound Action definition (e.g., via Creator Studio), create an input variable, say email_address of type string.
YAML Structure:
steps:
  - action:
      action_name: mw.get_user_by_email # Built-in action to fetch user details
      output_key: fetched_user_info
      input_args:
        user_email: data.email_address # Using the input variable
  - script:
      output_key: greeting_message
      input_args:
        user_full_name: data.fetched_user_info.user.full_name # Accessing a field from the previous action's output
      code: |
        # APIthon code to create a greeting
        greeting = "Hello, " + user_full_name + "! Welcome."
        greeting # This is the return value of the script
In this example, the first step calls mw.get_user_by_email, passing the data.email_address input. The result is stored in data.fetched_user_info. The second step, a script action, takes the full_name from data.fetched_user_info.user.full_name as an input argument (user_full_name) and constructs a greeting string, which is then stored in data.greeting_message.
4. Integrating Multiple Actions
Compound Actions excel at orchestrating sequences of different types of actions.
4.1. Calling HTTP Actions
HTTP Actions are fundamental for integrating Moveworks with external business systems and APIs (e.g., ServiceNow, Salesforce, Workday). Within a Compound Action, an HTTP Action is invoked by referencing its pre-configured action_name. The actual configuration of the HTTP Action (including endpoint URL, authentication method, headers, etc.) is typically done in the Creator Studio, separate from the Compound Action YAML.
YAML Snippet (Conceptual):
steps:
  - action:
      action_name: retrieve_customer_order_details # Name of a pre-configured HTTP Action
      output_key: customer_order_response
      input_args:
        order_id: data.input_order_id # Assuming 'input_order_id' is an input variable
        customer_segment: "premium"
      progress_updates:
        on_pending: "Retrieving order details for order ID: {{data.input_order_id}}..."
        on_complete: "Order details retrieved successfully."
  # A subsequent step could use data.customer_order_response
  - script:
      output_key: order_summary
      input_args:
        order_data: data.customer_order_response.body # Assuming response body contains order info
      code: |
        # APIthon to process order_data and create a summary
        summary = "Order Total: $" + str(order_data.total_amount) # Example
        summary
4.2. Using Basic Built-in Actions
Moveworks provides a range of built-in actions, also known as Native Actions, which can be directly invoked within Compound Actions using the mw.{{action_name}} syntax. These actions simplify common tasks such as sending chat notifications or retrieving user information.
Example:
Assume a previous step in the Compound Action has retrieved a user's record ID and stored it in data.target_user_object.user.id.
steps:
  #... previous steps...
  - action:
      action_name: mw.send_plaintext_chat_notification
      output_key: chat_notification_status # Stores the result of the send operation
      input_args:
        user_record_id: data.target_user_object.user.id # Target user's record ID
        message: "Your request has been processed. Reference ID: {{data.request_reference_id}}" # Personalized message
      progress_updates:
        on_pending: "Sending notification..."
        on_complete: "Notification sent."
Here, mw.send_plaintext_chat_notification is called to send a message. The user_record_id and message are provided as input arguments. The message itself can be dynamic, incorporating data from other parts of the Compound Action (like data.request_reference_id in this example) using {{}} template syntax.
5. Basic YAML for Sequential Logic
As outlined previously, the steps list is the fundamental construct for defining sequential logic. Actions listed under steps are executed one after another, in the order they appear in the YAML. The completion of one step triggers the execution of the next.
6. Handling Inputs and Outputs (output_key, basic data access)
Effective data flow is critical for multi-step automations. In Compound Actions, this is primarily managed through output_key and the data object.
•	output_key: Every action or script step that produces a result requires an output_key to be defined. This key serves as the variable name under which the step's output (e.g., API response, script return value) will be stored. If the output is not needed, use _.
•	data Object: The data object acts as a central repository or shared memory for the Compound Action's execution. It holds: 
o	The initial input variables provided to the Compound Action.
o	The outputs of all completed steps, stored under their respective output_keys.
Subsequent steps in the Compound Action can then access these stored values using the pattern data.your_output_key.some_field_in_the_output or data.your_input_variable.
Example of Data Flow:
steps:
  - action:
      action_name: get_initial_record # Assume this action returns { "id": "123", "status": "pending" }
      output_key: step1_result
      input_args:
        record_name: data.input_record_name
  - script:
      action_name: process_record_status # Note: script actions don't have an 'action_name' field, this is illustrative
      output_key: step2_processed_data
      input_args:
        current_status: data.step1_result.status # Accessing 'status' from step1_result
        record_identifier: data.step1_result.id # Accessing 'id' from step1_result
      code: |
        # APIthon code
        new_status_message = "Record " + record_identifier + " is currently " + current_status + "."
        new_status_message # This is returned
  - action:
      action_name: mw.send_plaintext_chat_notification
      output_key: notification_status
      input_args:
        user_record_id: data.requestor.user.id # Assuming requestor info is available via data.requestor
        message: data.step2_processed_data # Using the full output from step 2
In this flow, step1_result holds the output of the first action. The script in the second step uses fields from data.step1_result. Finally, the notification action uses the entire output from the script, data.step2_processed_data, as its message.
7. Beginner Practical Example: Fetching User Data and Sending a Notification
This complete example combines concepts from this beginner quickstart to perform a common IT support task: looking up a user and sending them a basic notification.
Scenario: An employee reports an issue, and an automated process needs to acknowledge this by sending them a personalized chat message.
Steps:
1.	Input Variable: The Compound Action will take the user's email address as input. Let's call it user_email_input.
2.	Fetch User Details: Use the mw.get_user_by_email built-in action to retrieve the user's full details, including their record ID (needed for sending a notification) and full name (for personalization).
3.	Send Notification: Use the mw.send_plaintext_chat_notification built-in action to send a message to the user.
YAML Implementation:
# Input variable defined in Creator Studio:
# - user_email_input (string), e.g., "[email protected]"
steps:
  - action:
      action_name: mw.get_user_by_email
      output_key: user_lookup_result
      input_args:
        user_email: data.user_email_input # Using the defined input variable
      progress_updates:
        on_pending: "Looking up user details for {{data.user_email_input}}..."
        on_complete: "User details retrieved."
  - action:
      action_name: mw.send_plaintext_chat_notification
      output_key: notification_dispatch_status
      input_args:
        # User record ID is required by send_plaintext_chat_notification
        # It's found within the output of mw.get_user_by_email
        user_record_id: data.user_lookup_result.user.id
        # Constructing a personalized message using the user's full name
        message: $CONCAT(["Hello ", data.user_lookup_result.user.full_name, ", we have received your request and will look into it shortly."], "", TRUE)
        # The $CONCAT is a Moveworks Data Mapping Syntax function.
        # Alternatively, a script action could be used for more complex message formatting.
      progress_updates:
        on_pending: "Sending acknowledgment to {{data.user_lookup_result.user.full_name}}..."
        on_complete: "Acknowledgment sent."
  - return: # Optionally, return a status or summary
      output_mapper:
        final_status: "'Notification sent to ' + data.user_lookup_result.user.full_name"
        user_record_id_processed: data.user_lookup_result.user.id
Explanation:
The Compound Action starts by using mw.get_user_by_email with the provided data.user_email_input. The output, containing the user object, is stored in data.user_lookup_result.
The second action, mw.send_plaintext_chat_notification, then uses data.user_lookup_result.user.id to target the correct user and data.user_lookup_result.user.full_name for a personalized message. The message concatenation is shown using $CONCAT, a function from Moveworks Data Mapping Syntax (DSL).
Finally, a return step is included to output a summary of the operation. This step is optional and demonstrates how you can control the final output of your Compound Action.
________________________________________
I hope this makes the document easier to read and better organized. Let me know if there's anything else you need!
