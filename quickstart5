Quickstart #5: Compound Actions: Advanced
This quickstart guide builds upon beginner concepts and explores more complex integrations, advanced data manipulation techniques (including APIthon scripting and Moveworks Data Mapping Syntax), and sophisticated workflow logic such as conditionals, loops, parallel processing, and robust error handling 1.
1. Mastering Control Flow
Beyond simple sequential execution, Compound Actions offer constructs to manage more intricate workflow logic 2.
1.1. Conditional Logic with switch
The switch expression provides a way to implement conditional branching, similar to if/else-if/else statements in programming languages. It evaluates a series of conditions and executes the steps associated with the first condition that evaluates to true 3.
A switch expression consists of:
•	cases: A list of dictionaries. Each dictionary must contain: 
o	condition: A boolean expression that is evaluated (e.g., data.user_details.access_level == 'admin') 4.
o	steps: A list of action/expression steps to execute if this condition is true 5.
•	default (optional): A dictionary containing a steps list to be executed if none of the conditions in cases evaluate to true 6.
YAML Snippet 1:
steps:
  # Assume a previous step populated data.user_details with an object like
  # { "id": "user123", "access_level": "admin", "department": "Engineering" }
  - switch:
      cases:
        - condition: data.user_details.access_level == 'admin'
          steps:
            - action:
                action_name: grant_admin_privileges
                output_key: admin_privileges_status
                input_args:
                  user_id: data.user_details.id
                  resource: "all_systems"
        - condition: data.user_details.access_level == 'editor' && data.user_details.department == 'Marketing'
          steps:
            - action:
                action_name: grant_marketing_editor_access
                output_key: editor_privileges_status
                input_args:
                  user_id: data.user_details.id
                  content_folder: "/marketing/campaigns"
      default:
        steps:
          - action:
              action_name: mw.send_plaintext_chat_notification
              output_key: default_notification_status
              input_args:
                user_record_id: data.user_details.id # Assuming ID is the record_id
                message: "Standard access level assigned."
1.2. Iteration with for Loops
The for loop expression enables iteration over an iterable collection (like a list). For each item in the collection, a defined set of steps can be executed 7. This is essential for processing multiple items.
A for loop requires the following fields 8:
•	each: A variable name that will represent the current item from the iterable during each iteration 9.
•	index: A variable name that will represent the zero-based index of the current item 10.
•	in: The name of the iterable variable (usually accessed from the data object, e.g., data.user_list) 11.
•	output_key: A variable name to store the results of the loop's execution (a list of outputs from each iteration) 12.
•	steps (optional): A list of Compound Action expressions to be executed on each element 13.
YAML Snippet 1:
steps:
  # Assume data.employee_list contains a list of employee objects:
  # e.g.,
  - for:
      each: employee_record
      index: employee_idx
      in: data.employee_list
      output_key: salary_adjustment_outcomes
      steps:
        - script:
            output_key: adjusted_salary_info # This key is local to the loop's iteration result
            input_args:
              current_salary: employee_record.salary
              employee_name: employee_record.name
            code: |
              # APIthon script to calculate a 5% raise
              new_salary = current_salary * 1.05
              result_map = {
                "name": employee_name,
                "old_salary": current_salary,
                "new_salary": new_salary
              }
              result_map
The output_key of the for loop will contain a list of the adjusted_salary_info dictionaries, one for each employee 14.
2. Advanced Data Manipulation
Compound Actions provide robust mechanisms for transforming, shaping, and manipulating data 15.
2.1. Leveraging APIthon script Actions (Deep Dive)
The script expression allows execution of custom code using APIthon, a Python-like scripting language, directly within a Compound Action 16. This provides flexibility for complex data transformations or custom business logic.
A script action minimally requires 17:
•	code: A string containing the APIthon code 18.
•	output_key: A variable name to store the script's return value 19.
•	input_args (optional): A dictionary mapping input argument names (used within the script) to their values 20.
APIthon Characteristics and Constraints 21:
•	Return Value: The result of the last line of executed code is returned 22.
•	No Imports: import statements are not allowed 23.
•	No Class Definitions: Custom class definitions are not supported 24.
•	No Private Members: Accessing methods or attributes starting with _ is generally disallowed 25.
•	Size Limits: Constraints exist on code length (e.g., 4096 bytes), list size (e.g., 2096 bytes), number magnitude, and string length (e.g., 4096 bytes/characters) 26.
•	Supported Operations: Common operations on strings, lists, sets, dictionaries, numbers, and standard built-in functions are supported 27.
YAML Snippet (Calculating statistics) 1:
steps:
  # Assume data.transaction_values is a list of numbers, e.g.,
  - script:
      output_key: transaction_stats
      input_args:
        numbers: data.transaction_values
      code: > # Using '>' for a multi-line script block
        sum_numbers = sum(numbers)
        count_numbers = len(numbers)
        average = 0
        if count_numbers > 0:
            average = sum_numbers / count_numbers
        stats_dict = {
          'sum': sum_numbers,
          'count': count_numbers,
          'average': average
        }
        stats_dict # This dictionary is returned
It's recommended to use DSL and our Data Mapper for common data transformation operations before resorting to Script Actions, as Script Actions can add latency 28.
2.2. Utilizing Moveworks Data Mapping Syntax (Bender/DSL)
Moveworks Data Mapping Syntax (DSL) provides a declarative way to manipulate and transform data directly within YAML, often in input_args of actions/scripts or output_mapper of return expressions 29.
This syntax allows for operations like string concatenation, case changes, conditional logic within mappings, and list operations 30. Functions like $CONCAT, $MAP, $LOWERCASE, and $TITLECASE are examples 30.
Example (Transforming a list of users) 8:
# Assume data.user_list contains:
# [{ "id": "u1", "first_name": "jane", "last_name": "doe"},
#  { "id": "u2", "first_name": "john", "last_name": "smith"}]
steps:
  - return:
      output_mapper:
        processed_users:
          MAP(): # Iterate over data.user_list
            items: data.user_list
            converter: # Define how each item is transformed
              user_id: item.id
              full_name: $CONCAT(, " ", TRUE)
        system_message: "'User processing complete.'"
Using DSL for common transformations can lead to cleaner YAML and potentially better performance compared to an APIthon script 31.
3. Orchestrating Complex Workflows
For more sophisticated scenarios, Compound Actions provide constructs for parallel execution and robust error handling 32.
3.1. Parallel Processing with parallel
The parallel expression enables concurrent execution of multiple expressions or loop iterations, which can optimize performance by running independent tasks simultaneously 33.
parallel can be used with:
•	for: To execute steps for each item in an iterable in parallel 34.
•	branches: To execute a predefined list of expressions concurrently 35.
YAML Snippet (Parallel branches) 1:
steps:
  # Assume data.requestor.id and data.system_config.api_version are available
  - parallel:
      branches:
        - action: # Branch 1
            action_name: fetch_user_permissions
            output_key: user_permissions_result
            input_args:
              user_id: data.requestor.id
        - action: # Branch 2
            action_name: fetch_global_settings
            output_key: global_settings_result
            input_args:
              version: data.system_config.api_version
  # Subsequent steps can access data.user_permissions_result and data.global_settings_result
  - action:
      action_name: process_combined_data
      output_key: final_processing_outcome
      input_args:
        permissions: data.user_permissions_result
        settings: data.global_settings_result
3.2. Effective Error Handling: try_catch and raise
Robust error handling is crucial for resilient automations 36.
try_catch: Executes a block of steps (the try block). If an error occurs, the catch block is executed 37. The catch block can use on_status_code to handle specific errors 38. Error details are typically available in an error_data object within the catch block 39.
raise: Explicitly stops execution by raising an error 40. Requires an output_key and an optional message 41.
YAML Snippet (try_catch with raise) 1:
steps:
  - try_catch:
      try:
        steps:
          - action:
              action_name: critical_external_api_call
              output_key: api_call_result # Error details will be in error_data.api_call_result
              input_args:
                resource_id: data.target_resource_id
      catch:
        on_status_code: ["E503"] # Example: Catch Service Unavailable
        steps:
          - action: # Log the failure
              action_name: log_critical_failure
              output_key: logging_outcome
              input_args:
                error_details: error_data.api_call_result # Accessing error data
          - raise: # Re-raise a standardized error
              message: "A critical system dependency failed. Operation could not complete."
              output_key: critical_dependency_failure
3.3. Using return for Early Exits
The return expression facilitates a graceful early exit from a Compound Action, stopping further execution and providing a specific output via output_mapper 42.
YAML Snippet (Early exit on validation failure) 1:
steps:
  - action:
      action_name: validate_request_data
      output_key: validation_outcome # e.g., { "isValid": false, "reason": "Missing field" }
      input_args:
        request_payload: data.incoming_request
  - switch:
      cases:
        - condition: data.validation_outcome.isValid == false
          steps:
            - return: # Early exit
                output_mapper:
                  status: "'VALIDATION_FAILED'"
                  error_message: data.validation_outcome.reason
  # These steps only execute if validation passed
  - action:
      action_name: process_validated_request
      output_key: processing_result
      input_args:
        payload: data.incoming_request
4. Working with Exposed Data Objects (data, requestor, mw, error_data)
Compound Actions operate with several key exposed data objects 43:
•	data: The central repository for input variables and outputs of completed steps (accessed via data.input_variable_name or data.output_key_of_a_step) 44.
•	requestor: Contains information about the user who initiated the action (e.g., requestor.email_addr, requestor.department) 45.
•	mw: Provides access to Moveworks Native (Built-in) Actions (e.g., mw.create_generic_approval_request) 46.
•	error_data: Contains information about errors from steps in ERROR or ERROR_HANDLED states, crucial for catch blocks (accessed like error_data.output_key_of_failed_step) 47.
Other less common fields include workflow_id, statuses, pending_data, and progress_updates 48.
5. Advanced Use of Built-in Actions
Moveworks offers sophisticated built-in actions (mw.action_name) for advanced scenarios 49.
Key advanced built-in actions include 50:
•	mw.create_generic_approval_request: Creates an in-bot approval request. 
o	Key Inputs: approval_details (string, Required), users_requested_for (List[User], Required), approvers (List[User], Optional) or approval_key (string, Optional). Requires User objects, not just emails 51.
•	mw.generate_structured_value_action: Uses an LLM to extract structured data from a payload based on a JSON schema. 
o	Key Inputs: payload (object, Required), output_schema (object, Required), system_prompt (string, Optional), model (string, Optional, e.g., "4o-mini") 52.
•	mw.generate_text_action: Calls an LLM to generate free-form text. 
o	Key Inputs: user_input (string, Required), system_prompt (string, Optional), model (string, Optional) 53.
•	User Retrieval Actions (mw.get_user_by_email, mw.batch_get_users_by_email): Essential for fetching User objects needed by other built-in actions 54.
These LLM-powered actions allow developers to easily incorporate AI-driven data processing and text generation into workflows 55.
6. Advanced Practical Example: Multi-step Approval Workflow
This example demonstrates a software access request workflow 56:
•	Inputs: software name, justification, optional custom approver email 57.
•	Determine approver (custom or requester's manager) 58.
•	Fetch approver's User object 59.
•	Format approval details 60.
•	Send approval request using mw.create_generic_approval_request 61.
•	Conditionally act based on approval status (Approved/Denied) using switch 62.
•	Handle errors using try_catch 63.
Conceptual YAML Snippet:
# Input variables: software_name, justification, custom_approver_email (optional)
steps:
  # 1. Get Requester's Details (including manager_email_addr)
  - action:
      action_name: mw.get_user_by_email
      output_key: requestor_details
      input_args:
        user_email: requestor.email_addr # Using requestor object
  # 2. Determine Approver Email (Script or DSL)
  - script:
      output_key: approver_determination
      input_args:
        custom_email: data.custom_approver_email
        manager_email: data.requestor_details.user.manager_email_addr
      code: |
        approver_email_to_use = custom_email if custom_email else manager_email
        if not approver_email_to_use:
          raise ValueError("Approver email cannot be determined.")
        {"final_approver_email": approver_email_to_use}
  # 3. Get Approver's User Object
  - action:
      action_name: mw.get_user_by_email #
      output_key: approver_user_object
      input_args:
        user_email: data.approver_determination.final_approver_email
  # 4. Prepare Approval Details (Script for formatting)
  - script:
      output_key: formatted_approval_request
      input_args:
        sw_name: data.software_name
        user_justification: data.justification
        requesting_user_name: data.requestor_details.user.full_name
      code: |
        details = "Software: " + sw_name + "\nRequested by: " + requesting_user_name + "\nJustification: " + user_justification
        details
  # 5. Send Approval Request
  - try_catch: # [1, 2]
      try:
        steps:
          - action:
              action_name: mw.create_generic_approval_request #
              output_key: approval_submission_result
              input_args:
                approvers: [data.approver_user_object.user] # Must be a list of User objects
                approval_details: data.formatted_approval_request
                users_requested_for: [data.requestor_details.user]
      catch:
        steps:
          - action:
              action_name: mw.send_plaintext_chat_notification #
              output_key: _
              input_args:
                user_record_id: data.requestor_details.user.id
                message: "Error submitting approval. Details: {{error_data.approval_submission_result.error.message}}"
          - raise: # [1, 2]
              message: "Failed to create approval request."
              output_key: approval_creation_failure
  # 6. Process Approval Outcome
  - switch: # [1, 2]
      cases:
        - condition: data.approval_submission_result.status == 'APPROVED'
          steps:
            - action: # Mocked: Provision software (HTTP Action)
                action_name: provision_software_api
                output_key: _
                input_args: { user_id: data.requestor_details.user.id, software: data.software_name }
            - action:
                action_name: mw.send_plaintext_chat_notification
                output_key: _
                input_args:
                  user_record_id: data.requestor_details.user.id
                  message: $CONCAT(["Your request for ", data.software_name, " has been approved and is being provisioned."], "", TRUE)
        - condition: data.approval_submission_result.status == 'DENIED'
          steps:
            - action:
                action_name: mw.send_plaintext_chat_notification
                output_key: _
                input_args:
                  user_record_id: data.requestor_details.user.id
                  message: $CONCAT(["Your request for ", data.software_name, " has been denied."], "", TRUE) # Denial reason could be added if available
      default:
        steps:
          - action:
              action_name: mw.send_plaintext_chat_notification
              output_key: _
              input_args:
                user_record_id: data.requestor_details.user.id
                message: "Unexpected status for your software request approval."
  # 7. Final Return (Optional)
  - return: #
      output_mapper:
        workflow_status: data.approval_submission_result.status
This advanced guide provides a deeper look into the capabilities of Moveworks Compound Actions, enabling you to build more powerful and intelligent automations 64.
Would you like more specific advice on any particular section of your document?

